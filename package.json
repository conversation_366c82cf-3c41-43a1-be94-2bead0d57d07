{"name": "Netme", "version": "1.0.0", "description": "backened", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "stripe:listen": "stripe listen --forward-to=localhost:4576/stripe/webhook", "test": "jest"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.693.0", "@aws-sdk/client-ses": "^3.616.0", "@aws-sdk/credential-providers": "^3.616.0", "aws-sdk": "^2.1359.0", "axios": "^1.4.0", "axios-rate-limit": "^1.4.0", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "braintree": "^3.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^8.2.0", "ejs": "^3.1.6", "express": "^4.18.2", "express-fileupload": "^1.2.1", "express-validator": "^6.6.1", "firebase": "^9.18.0", "firebase-admin": "^11.11.1", "haversine": "^1.1.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.48", "mongoose": "^6.10.5", "mongoose-delete": "^0.5.4", "mongoose-paginate-v2": "^1.3.17", "mongoose-timeseries": "^1.3.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "node-schedule": "^2.1.1", "nodemailer": "^6.9.1", "sharp": "^0.33.5", "socket.io": "^4.8.1", "stripe": "^14.7.0", "validator": "^13.1.17", "winston": "^3.8.2"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^9.1.3", "nodemon": "^3.0.1"}}