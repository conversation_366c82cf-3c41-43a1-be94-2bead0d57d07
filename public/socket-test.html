<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetMe Socket.IO Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }

        .message {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
        }

        .received {
            background-color: #e3f2fd;
        }

        .sent {
            background-color: #e8f5e9;
            text-align: right;
        }

        .info {
            background-color: #fff3e0;
            font-style: italic;
        }

        .error {
            background-color: #ffebee;
            color: #c62828;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        input,
        button,
        select {
            padding: 8px;
        }

        input {
            flex-grow: 1;
        }

        .status {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <h1>NetMe Socket.IO Test</h1>

    <div class="container">
        <h2>Connection</h2>
        <div class="controls">
            <input type="text" id="token" placeholder="Enter JWT token" style="width: 70%;">
            <button id="connect">Connect</button>
            <button id="disconnect">Disconnect</button>
        </div>
        <div class="status" id="connection-status">Not connected</div>
    </div>

    <div class="container">
        <h2>Join Chat</h2>
        <div class="controls">
            <input type="text" id="chat-id" placeholder="Enter Chat ID">
            <button id="join-chat">Join Chat</button>
        </div>
        <div class="status" id="chat-status">Not joined to any chat</div>
    </div>

    <div class="container">
        <h2>Send Message</h2>
        <div class="controls">
            <input type="text" id="message" placeholder="Type a message">
            <button id="send-message">Send</button>
        </div>
    </div>

    <div class="container">
        <h2>Mark Messages as Read</h2>
        <div class="controls">
            <button id="mark-read">Mark All Messages as Read</button>
        </div>
    </div>

    <div class="container">
        <h2>Messages</h2>
        <div class="messages" id="messages"></div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // DOM elements
        const tokenInput = document.getElementById('token');
        const connectBtn = document.getElementById('connect');
        const disconnectBtn = document.getElementById('disconnect');
        const connectionStatus = document.getElementById('connection-status');
        const chatIdInput = document.getElementById('chat-id');
        const joinChatBtn = document.getElementById('join-chat');
        const chatStatus = document.getElementById('chat-status');
        const messageInput = document.getElementById('message');
        const sendMessageBtn = document.getElementById('send-message');
        const markReadBtn = document.getElementById('mark-read');
        const messagesContainer = document.getElementById('messages');

        // Socket instance
        let socket = null;
        let currentChatId = null;

        // Connect to Socket.IO server
        connectBtn.addEventListener('click', () => {
            const token = tokenInput.value.trim();
            if (!token) {
                showError('Please enter a JWT token');
                return;
            }

            try {
                // Disconnect existing socket if any
                if (socket) {
                    socket.disconnect();
                }

                // Connect with token
                connectionStatus.textContent = 'Connecting...';
                socket = io({
                    auth: {
                        token: token
                    },
                    transports: ['websocket'],
                    reconnection: true
                });

                // Connection events
                socket.on('connect', () => {
                    connectionStatus.textContent = `Connected (Socket ID: ${socket.id})`;
                    addMessage('Connected to server', 'info');
                });

                socket.on('connection_success', (data) => {
                    connectionStatus.textContent = `Connected as user ${data.userId} (Socket ID: ${data.socketId})`;
                    addMessage(`Connection success: ${data.message}`, 'info');
                });

                socket.on('disconnect', () => {
                    connectionStatus.textContent = 'Disconnected';
                    addMessage('Disconnected from server', 'info');
                });

                socket.on('connect_error', (error) => {
                    connectionStatus.textContent = `Connection error: ${error.message}`;
                    addMessage(`Connection error: ${error.message}`, 'error');
                });

                // Chat events
                socket.on('chat_status', (data) => {
                    chatStatus.textContent = `Joined chat: ${data.chatId} (Status: ${data.status})`;
                    addMessage(`Chat status: ${data.statusMessage}`, 'info');
                });

                socket.on('new_message', (data) => {
                    console.log("data", data)
                    addMessage(`${data.sender}: ${data.content}`, 'received');
                });

                socket.on('message_sent', (data) => {
                    addMessage(`Message sent successfully (ID: ${data.messageId})`, 'info');
                });

                socket.on('messages_read', (data) => {
                    addMessage(`Messages marked as read in chat ${data.chatId} by user ${data.userId} (${data.count} messages)`, 'info');
                });

                socket.on('mark_read_confirmed', (data) => {
                    addMessage(`Mark read confirmed: ${data.count} messages in chat ${data.chatId}`, 'info');
                });

                socket.on('error', (data) => {
                    addMessage(`Error: ${data.message}`, 'error');
                });
            } catch (error) {
                connectionStatus.textContent = `Error: ${error.message}`;
            }
        });

        // Disconnect from server
        disconnectBtn.addEventListener('click', () => {
            if (socket) {
                socket.disconnect();
                socket = null;
                currentChatId = null;
                connectionStatus.textContent = 'Disconnected';
                chatStatus.textContent = 'Not joined to any chat';
                addMessage('Manually disconnected from server', 'info');
            }
        });

        // Mark messages as read
        markReadBtn.addEventListener('click', () => {
            if (!socket) {
                showError('Please connect to the server first');
                return;
            }
            if (!currentChatId) {
                showError('Please join a chat first');
                return;
            }

            // Send mark_read event
            socket.emit('mark_read', { chatId: currentChatId });
            addMessage(`Sent mark_read request for chat ${currentChatId}`, 'info');
        });

        // Join a chat room
        joinChatBtn.addEventListener('click', () => {
            const chatId = chatIdInput.value.trim();
            if (!socket) {
                showError('Please connect to the server first');
                return;
            }

            if (!chatId) {
                showError('Please enter a chat ID');
                return;
            }

            currentChatId = chatId;
            socket.emit('join_chat', { chatId });
            chatStatus.textContent = `Joining chat ${chatId}...`;
            addMessage(`Attempting to join chat: ${chatId}`, 'info');
        });

        // Send a message
        sendMessageBtn.addEventListener('click', () => {
            const message = messageInput.value.trim();
            if (!socket) {
                showError('Please connect to the server first');
                return;
            }

            if (!currentChatId) {
                showError('Please join a chat first');
                return;
            }

            if (!message) {
                showError('Please enter a message');
                return;
            }

            socket.emit('send_message', {
                chatId: currentChatId,
                content: message,
                isAudio: false,
                audioUrl: null,
                isInfo: false
            });

            messageInput.value = '';
        });

        // Helper functions
        function addMessage(text, type = 'info') {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', type);
            messageElement.textContent = text;
            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showError(message) {
            addMessage(message, 'error');
        }
    </script>
</body>

</html>