const mongoose = require("mongoose");
const haversine = require("haversine");
const User = require("../../models/User/User");
const Bundle = require("../../models/User/bundle");
const Invitation = require("../../models/User/invitations");
const Business = require("../../models/Partner/partnerBusiness");
const UserNotification = require("../../models/User/userNotification");
const feedback = require("../../models/User/feedback");
const { sendEmail } = require("../../util/sendEmail");
const { body, validationResult } = require("express-validator");
const { getNotificationText } = require("../../util/getNotificationText");
const { getGoogleNearByPlaces } = require("../../util/getGoogleNearByPlaces");
const {
  getAggregatedInvitaion,
  getAggregatedInvitationsRequests,
} = require("../../util/getAggregatedInvitation");
const { checkUserIsPremium } = require("../../util/checkUserIsPremium");
const Feedback = require("../../models/User/feedback");
const { sendPushNotification } = require("../../util/sendPushNotification");
//const { default: isEmail } = require("validator/lib/isEmail");

module.exports.getBusinessUnderRadius = async (req, res) => {
  try {
    const { coordinates, category, distance, rating, is_open, price_level } =
      req.body;
    let radius;
    let obj = {};
    if (distance) {
      radius = parseInt(distance) * 1000;
    }
    const currentDay = new Date()
      .toLocaleString("en-US", { weekday: "short" })
      .slice(0, 3); // Get current day name (e.g., "Monday")
    const currentTime = new Date().toTimeString().slice(0, 5); // Get current time (e.g., "13:45")

    const data = await Business.aggregate([
      {
        $geoNear: {
          near: {
            type: "Point",
            coordinates: coordinates,
          },
          distanceField: "distance",
          maxDistance: Number(radius),
          spherical: true,
        },
      },
      {
        $match: {
          ...obj,
          isPartnerActive: true,
          deleted: false,
          ...(Array.isArray(category) && category.length > 0
            ? { category: { $in: category } }
            : {}),
          ...(rating !== 0 ? { rating: { $gte: Number(rating) } } : {}), // Filter by minimum rating
          ...(price_level !== 0 ? { priceLevel: Number(price_level) } : {}), // Filter by price level
        },
      },
      {
        $project: {
          name: 1,
          category: 1,
          location: 1,
          distance: 1,
          city: 1,
          rating: 1,
          priceLevel: 1,
          taxNumber: 1,
          businessEmail: 1,
          businessMobile: 1,
          businessTel: 1,
          address: 1,
          isPartnerActive: 1,
          partnerId: 1,
          businessSchedule: 1,
          photos: 1,
          // is_open: {
          //   $anyElementTrue: {
          //     $map: {
          //       input: "$businessSchedule",
          //       as: "schedule",
          //       in: {
          //         $and: [
          //           { $eq: ["$$schedule.day", currentDay] }, // Check if today matches schedule day
          //           { $eq: ["$$schedule.status", true] }, // Business should be open (status: true)
          //           {
          //             $lte: ["$$schedule.from", currentTime], // Check if current time is >= from time
          //           },
          //           {
          //             $gte: ["$$schedule.to", currentTime], // Check if current time is <= to time
          //           },
          //         ],
          //       },
          //     },
          //   },
          // },
        },
      },
      // ...(typeof is_open !== "undefined"
      //   ? [
      //     {
      //       $match: {
      //         is_open: is_open === true, // Check if is_open matches the user's filter
      //       },
      //     },
      //   ]
      //   : []),
      {
        $limit: 80,
      },
    ]);
    if (data) {
      let finalData = [];
      if (data.length >= 80) {
        finalData = data;
      } else {
        if (!category) {
          const mapData = await getGoogleNearByPlaces(
            coordinates,
            radius,
            price_level
          );
          finalData = [...data, ...mapData.slice(0, 80 - data.length)];
        } else {
          try {
            const mapData = await getGoogleNearByPlaces(
              coordinates,
              radius,
              category,
              is_open,
              price_level
            );
            finalData = [...data, ...mapData.slice(0, 80 - data.length)];
          } catch (error) {
            if (error.message.includes("quota exceeded")) {
              return res.status(503).json({
                error:
                  "Service temporarily unavailable. Please try again later.",
              });
            } else {
              throw error;
            }
          }
        }
      }

      // sort final data on basis of the distance
      finalData = finalData.sort((a, b) => a.distance - b.distance);

      res.status(200).json({ data: finalData });
    } else throw Error("Data not found");
    //res.status(200).json(data);
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};

// module.exports.getBusinessUnderRadiusByFilter = async (req, res) => {
//   try {
//     const { coordinates, category, distance, is_open } = req.body;
//     let radius = req.body.radius;
//     let obj = {};
//     const cacheKey = `${coordinates}-${radius}`;
//     // Check cache

//     if(category){
//       obj["category"] = category
//     }
//     if(isEmailVerified){
//       obj["isEmailVerified"] = isEmailVerified
//     }
//     if(distance){
//       radius = parseInt(distance)* 1000;
//     }

//     const data = await Business.aggregate([
//       {
//         $geoNear: {
//           near: {
//             type: "Point",
//             coordinates: coordinates,
//           },
//           distanceField: "distance",
//           maxDistance: Number(radius),
//           spherical: true,
//         },
//       },
//       {
//         $match: {
//           ...obj,
//           isPartnerActive: true,
//           deleted: false,
//         },
//       },
//       {
//         $limit: 80,
//       },
//     ]);
//     if (data) {
//       let finalData = [];
//       if (data.length >= 80) {
//         finalData = data;
//       } else {
//         if(!category){
//           const mapData = await getGoogleNearByPlaces(coordinates, radius);
//           finalData = [...data, ...mapData.slice(0, 80 - data.length)];
//         }
//         else{
//           console.log(category, 'categr');
//           const mapData = await getGoogleNearByPlaces(coordinates, radius, category, is_open) ;
//           finalData = [...data, ...mapData.slice(0, 80 - data.length)];
//         }

//         // businessCache.set(cacheKey, finalData);
//       }
//       res.status(200).json({ data: finalData, length: finalData.length });
//     } else throw Error("Data not found");
//   } catch (err) {
//     let error = err.message;
//     res.status(400).json({ error: error });
//   }
// };
module.exports.getAllPlaces = async (req, res) => {
  try {
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    let skipValue = (page - 1) * limit;
    let obj = {};
    let { _id } = req.query;
    if (_id) {
      obj["_id"] = req.query;
    }
    const data = await Business.find({
      deleted: false,
      ...obj,
      isPartnerActive: true,
    })
      .populate("partnerId")
      .skip(skipValue)
      .limit(limit)
      .sort({ createdAt: -1 });

    let count = await Business.find({
      deleted: false,
      isPartnerActive: true,
      ...obj,
    }).countDocuments();

    res.status(200).json({
      data: _id ? (data && data.length > 0 ? data[0] : false) : data,
      totalData: count,
      totalPage: Math.ceil(count / limit),
      perPage: limit,
      currentPage: page,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

const checkIsDailyInviteFinish = async (userId) => {
  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);
  const endOfDay = new Date(currentDate);
  endOfDay.setHours(23, 59, 59, 999);
  const invitationCount = await Invitation.countDocuments({
    invitationBy: userId,
    createdAt: { $gte: currentDate, $lt: endOfDay },
  });
  await User.updateOne(
    { _id: userId },
    { $set: { dailyInviteCount: invitationCount } }
  );
  return invitationCount;
};

module.exports.inviteUser = [
  // Enhanced validation
  body("users")
    .isArray({ min: 1 })
    .withMessage("At least one user is required"),
  body("date").isISO8601().withMessage("Valid date is required"),
  body("time")
    .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage("Valid time format (HH:MM) is required"),

  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { users, date, time, confirmCase2B, invitationId, note } = req.body;
      const invitationBy = req.user._id;
      if (!!confirmCase2B) {
        const cancelInvite = await Invitation.findByIdAndUpdate(invitationId, {
          status: "Rejected",
          deleted: true,
        });
        //console.log("cancelInvite", cancelInvite);
      }

      // Validate user has cover image
      const user = await User.findById(invitationBy);
      if (!user.coverImage) {
        return res.status(400).json({
          error: "You must have a cover image to send an invitation.",
        });
      }

      // Parse and validate time
      const newTimeParts = extractTimeParts(time);
      if (!newTimeParts) {
        return res.status(400).json({
          error: "Invalid time format. Please use HH:MM format.",
        });
      }

      // Create date object in UTC to avoid timezone issues
      const newDateTime = new Date(`${date}T${time}:00Z`);
      if (isNaN(newDateTime.getTime())) {
        return res.status(400).json({
          error: "Invalid date/time combination.",
        });
      }

      // Check if the selected date is within 7 days from now
      const currentDate = new Date();
      const sevenDaysFromNow = new Date(currentDate);
      sevenDaysFromNow.setDate(currentDate.getDate() + 7);
      console.log(
        "newDateTime > sevenDaysFromNow",
        newDateTime,
        sevenDaysFromNow
      );
      if (newDateTime > sevenDaysFromNow) {
        return res.status(400).json({
          error:
            "You can only schedule meetings up to 7 days in advance. Please select an earlier date.",
        });
      }

      // Check for blocked period conflicts (3 hours before and after existing invitations)
      const blockedPeriodCheck = await checkForBlockedPeriodConflicts(
        invitationBy,
        newDateTime
      );
      console.log("blockedPeriodCheck", blockedPeriodCheck);
      if (blockedPeriodCheck.hasConflict) {
        const conflictTime =
          blockedPeriodCheck.conflictingDateTime.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });
        console.log("blockedPeriodCheck sss", blockedPeriodCheck);
        const conflictDate =
          blockedPeriodCheck.conflictingDateTime.toLocaleDateString();

        return res.status(409).json({
          error: `You cannot schedule a meetup at this time because it conflicts with the blocked period of your existing invitation with ${blockedPeriodCheck.conflictingUserName} on ${conflictDate} at ${conflictTime}. Please select a time that is at least 3 hours before or after your existing invitation.`,
          case: "BLOCKED_PERIOD_CONFLICT",
        });
      }
      // NEW CHECK 1: Prevent duplicate invitations to same user(s) - only for active invitations
      const now = new Date();
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      // Only check for active invitations (Pending, Accepted) that are current or future
      // Exclude completed invitations (those with feedback given)
      const existingActiveInvitations = await Invitation.find({
        invitationBy,
        "users.userId": { $in: users.map((u) => u.userId) },
        status: { $in: ["Pending", "Accepted"] }, // Only active statuses
        deleted: { $ne: true },
        // Only consider current and future invitations
        date: { $gte: now },
      }).populate("feedbackId");

      // Filter out invitations that are completed (have feedback given)
      const activeInvitations = existingActiveInvitations.filter((inv) => {
        // If no feedback or feedback not given, consider it active
        return !inv.feedbackId || !inv.feedbackId.given;
      });

      console.log("activeInvitations", activeInvitations);

      if (activeInvitations.length > 0) {
        const duplicateUsers = new Set(); // Using Set to avoid duplicates

        for (const inv of activeInvitations) {
          for (const u of inv.users) {
            // Convert both IDs to strings for comparison
            const existingUserIdStr = u.userId.toString();

            // Check if this user is in our new invitation list
            const isDuplicate = users.some((user) => {
              const newUserIdStr = user.userId.toString();
              return newUserIdStr === existingUserIdStr;
            });

            if (isDuplicate) {
              duplicateUsers.add(existingUserIdStr);
            }
          }
        }

        if (duplicateUsers.size > 0) {
          // Convert Set back to array of ObjectIds
          const duplicateUserIds = Array.from(duplicateUsers).map((id) =>
            mongoose.Types.ObjectId(id)
          );

          const duplicateUserNames = await User.find({
            _id: { $in: duplicateUserIds },
          }).select("userName");

          return res.status(409).json({
            error: `You’ve already invited: ${duplicateUserNames
              .map((u) => u.userName)
              .join(", ")}. Please wait until the current invitation ends.`,
            case: "DUPLICATE_INVITATION",
          });
        }
      }

      // NEW CHECK 2: Prevent inviting users who already have pending invitations from you
      const usersWithPendingInvites = await Invitation.find({
        invitationBy,
        "users.userId": { $in: users.map((u) => u.userId) },
        "users.status": "Pending",
        deleted: { $ne: true },
        date: { $gte: startOfDay, $lt: endOfDay },
      }).distinct("users.userId");

      if (usersWithPendingInvites.length > 0) {
        const userNames = await User.find({
          _id: { $in: usersWithPendingInvites },
        }).select("userName");

        return res.status(409).json({
          error: `The following users already have pending invitations from you: ${userNames
            .map((u) => u.userName)
            .join(", ")}`,
          case: "PENDING_INVITATION_EXISTS",
        });
      }
      // Check for existing active (accepted) invitations between these users
      // Only check for truly active invitations (not completed ones with feedback)
      const existingAcceptedInvitations = await Invitation.find({
        $or: [
          {
            invitationBy: invitationBy,
            "users.userId": { $in: users.map((u) => u.userId) },
            "users.status": "Accepted",
            deleted: { $ne: true },
            date: { $gte: new Date() }, // Only future or today's dates
          },
          {
            invitationBy: { $in: users.map((u) => u.userId) },
            "users.userId": invitationBy,
            "users.status": "Accepted",
            deleted: { $ne: true },
            date: { $gte: new Date() }, // Only future or today's dates
          },
        ],
      }).populate("feedbackId");

      // Filter out completed invitations (those with feedback given)
      const activeAcceptedInvitations = existingAcceptedInvitations.filter(
        (inv) => {
          return !inv.feedbackId || !inv.feedbackId.given;
        }
      );

      if (activeAcceptedInvitations.length > 0) {
        // Get unique user IDs from the found invitations
        const userIds = new Set();
        activeAcceptedInvitations.forEach((inv) => {
          if (inv.invitationBy.toString() !== invitationBy.toString()) {
            userIds.add(inv.invitationBy.toString());
          }
          inv.users.forEach((u) => {
            if (
              u.userId.toString() !== invitationBy.toString() &&
              u.status === "Accepted"
            ) {
              userIds.add(u.userId.toString());
            }
          });
        });

        // Get usernames for the error message
        const usersWithActiveInvites = await User.find({
          _id: {
            $in: Array.from(userIds).map((id) => mongoose.Types.ObjectId(id)),
          },
        }).select("userName");

        if (usersWithActiveInvites.length > 0) {
          return res.status(409).json({
            error: `You already have an active invitation with: ${usersWithActiveInvites
              .map((u) => u.userName)
              .join(
                ", "
              )}. Please complete or cancel the existing invitation before sending a new one.`,
            case: "ACTIVE_INVITATION_EXISTS",
          });
        }
      }

      // CASE 1: Check sender's existing invitations - only active ones
      const [sentInvitations, acceptedInvitations] = await Promise.all([
        Invitation.find({
          invitationBy,
          date: { $gte: startOfDay, $lt: endOfDay },
          status: { $in: ["Pending", "Accepted"] },
          deleted: { $ne: true },
        }).populate("feedbackId"),
        Invitation.find({
          "users.userId": invitationBy,
          "users.status": "Accepted",
          date: { $gte: startOfDay, $lt: endOfDay },
          deleted: { $ne: true },
        }).populate("feedbackId"),
      ]);

      // Filter out completed invitations (those with feedback given)
      const activeSentInvitations = sentInvitations.filter((inv) => {
        return !inv.feedbackId || !inv.feedbackId.given;
      });

      const activeReceivedInvitations = acceptedInvitations.filter((inv) => {
        return !inv.feedbackId || !inv.feedbackId.given;
      });

      const allUserInvitations = [
        ...activeSentInvitations,
        ...activeReceivedInvitations,
      ];

      // Check for time conflicts
      for (const invitation of allUserInvitations) {
        const existingTimeParts = extractTimeParts(invitation.time);
        if (!existingTimeParts) continue;

        const existingDateTime = new Date(invitation.date);
        existingDateTime.setHours(
          existingTimeParts[0],
          existingTimeParts[1],
          0,
          0
        );

        const diffInHours = Math.abs((newDateTime - existingDateTime) / 36e5);

        if (diffInHours < 1) {
          return res.status(409).json({
            error:
              "You have already sent an invitation or scheduled a meeting for this time. Please choose a different time.",
            case: "CASE_1",
          });
        }
      }

      // CASE 2: Handle received invitations
      //console.log("users.length", users.length);
      if (users.length === 1) {
        const invitee = users[0];

        // CASE 2B: Check if invitee has sent an invitation to current user
        const inviteeToUserInvitation = await Invitation.findOne({
          invitationBy: invitee.userId,
          "users.userId": invitationBy,
          "users.status": "Pending",
          date: { $gte: startOfDay, $lt: endOfDay },
          deleted: { $ne: true },
        }).populate("invitationBy", "userName");

        if (inviteeToUserInvitation) {
          const existingTimeParts = extractTimeParts(
            inviteeToUserInvitation.time
          );
          if (existingTimeParts) {
            const existingDateTime = new Date(inviteeToUserInvitation.date);
            existingDateTime.setHours(
              existingTimeParts[0],
              existingTimeParts[1],
              0,
              0
            );

            const diffInHours = Math.abs(
              (newDateTime - existingDateTime) / 36e5
            );

            if (diffInHours < 1) {
              const inviteeName = await User.findById(invitee.userId).select(
                "userName"
              );
              return res.status(409).json({
                error: `You have already sent an invitation to ${inviteeName.userName} for this time.\nIf you accept this invitation, your invitation to ${inviteeName.userName} will be automatically canceled.`,
                case: "CASE_2B",
                buttons: [
                  "Accept Invitation",
                  "Cancel – Decline this invitation",
                ],
                invitationId: inviteeToUserInvitation._id,
              });
            }
          }
        }
      }

      // CASE 2A: Check received pending invitations
      let receivedPendingQuery = {
        "users.userId": invitationBy,
        "users.status": "Pending",
        date: { $gte: startOfDay, $lt: endOfDay },
        deleted: { $ne: true },
      };

      if (users.length === 1) {
        receivedPendingQuery.invitationBy = { $ne: users[0].userId };
      }

      const receivedPendingInvitations = await Invitation.find(
        receivedPendingQuery
      ).populate("invitationBy", "userName");

      for (const invitation of receivedPendingInvitations) {
        const existingTimeParts = extractTimeParts(invitation.time);
        if (!existingTimeParts) continue;

        const existingDateTime = new Date(invitation.date);
        existingDateTime.setHours(
          existingTimeParts[0],
          existingTimeParts[1],
          0,
          0
        );

        const diffInHours = Math.abs((newDateTime - existingDateTime) / 36e5);

        if (diffInHours < 1) {
          return res.status(409).json({
            error:
              "Warning – You have already received an invitation for this time. Do you still want to send the invitation?",
            case: "CASE_2A",
            buttons: ["Yes, send it anyway", "Cancel"],
          });
        }
      }

      // Check invitee availability - improved logic for group vs individual invitations
      // const isGroupInvitation = users.length > 1;

      // for (const invitee of users) {
      //   // Get active invitations for this invitee on the same date
      //   const existingInvitations = await Invitation.find({
      //     "users.userId": invitee.userId,
      //     date: { $gte: startOfDay, $lt: endOfDay },
      //     status: { $in: ["Pending", "Accepted"] },
      //     deleted: { $ne: true },
      //   })
      //     .populate("feedbackId")
      //     .sort({ createdAt: -1 });

      //   // Filter out completed invitations
      //   const activeInvitations = existingInvitations.filter((inv) => {
      //     return !inv.feedbackId || !inv.feedbackId.given;
      //   });

      //   for (const existing of activeInvitations) {
      //     const existingTimeParts = extractTimeParts(existing.time);
      //     if (!existingTimeParts) continue;

      //     const existingDateTime = new Date(existing.date);
      //     existingDateTime.setHours(
      //       existingTimeParts[0],
      //       existingTimeParts[1],
      //       0,
      //       0
      //     );

      //     const diffInHours = Math.abs((newDateTime - existingDateTime) / 36e5);

      //     // For group invitations, only check same-day conflicts with time overlap
      //     // For individual invitations, check 1-hour window
      //     const conflictThreshold = isGroupInvitation ? 0.5 : 1; // 30 minutes for group, 1 hour for individual

      //     if (diffInHours < conflictThreshold) {
      //       const inviteeUser = await User.findById(invitee.userId);
      //       const conflictType = isGroupInvitation ? "group" : "individual";
      //       const timeWindow = isGroupInvitation ? "30 minutes" : "1 hour";

      //       return res.status(409).json({
      //         error: `User ${inviteeUser.userName} has an existing ${
      //           existing.isGroup ? "group" : "individual"
      //         } invitation within ${timeWindow} of your requested ${conflictType} invitation time. Please choose a different time.`,
      //         case: "INVITEE_CONFLICT",
      //         conflictDetails: {
      //           existingType: existing.isGroup ? "group" : "individual",
      //           newType: conflictType,
      //           timeConflict: diffInHours < conflictThreshold,
      //         },
      //       });
      //     }
      //   }
      // }

      // Check invitation limits
      const isPremiumUser = await checkUserIsPremium(invitationBy);
      let isFreeInvitation = false;
      let isUserHaveBundle = false;

      if (!isPremiumUser) {
        const dailyInviteCount = await checkIsDailyInviteFinish(invitationBy);
        if (dailyInviteCount < 2) {
          isFreeInvitation = true;
        } else {
          const findBundle = await Bundle.findOne({ userId: invitationBy });
          if (findBundle?.pendingInvitation > 0) {
            isUserHaveBundle = true;
          }
        }
      }

      if (!isFreeInvitation && !isPremiumUser && !isUserHaveBundle) {
        return res.status(403).json({
          error:
            "Please purchase a subscription or any plan to invite more people",
        });
      }

      // Create the invitation
      const feedback = await Feedback.create({
        userId: invitationBy,
        given: false,
        status: "no",
      });

      const invitePeople = await Invitation.create({
        ...req.body,
        date: newDateTime,
        feedbackId: feedback._id,
        invitationBy,
        isGroup: users.length > 1,
        note,
      });

      // Update bundle if used
      if (isUserHaveBundle) {
        await Bundle.findOneAndUpdate(
          { userId: invitationBy },
          { $inc: { pendingInvitation: -1 } }
        );
      }

      // Send notifications
      await Promise.all(
        users.map(async (invitee) => {
          await UserNotification.create({
            title: user.userName,
            body: getNotificationText()["Sent_Invitation"],
            image: user.coverImage,
            userId: invitee.userId,
          });

          const inviteeUser = await User.findById(invitee.userId);
          if (inviteeUser?.fcmToken) {
            await sendPushNotification({
              title: `from ${user.userName}`,
              body: `Hello ${inviteeUser.userName}, you have received an invitation.`,
              fcmToken: inviteeUser.fcmToken,
              userType: "USER",
            });
          }

          // Send email
          await sendEmail({
            from: `"NETME App" <${process.env.USER_APP_EMAIL}>`,
            to: inviteeUser.email,
            subject: "Invitation Notification",
            text: `Hey, ${user.userName} has sent you an invitation`,
          });
        })
      );

      res.status(201).json({ invitePeople });
    } catch (err) {
      console.error("Error in inviteUser:", err);
      res.status(500).json({
        error: "Failed to process invitation",
        details:
          process.env.NODE_ENV === "development" ? err.message : undefined,
      });
    }
  },
];

// module.exports.inviteUser = [
//   body("users").not().isEmpty().withMessage("users Field is required"),
//   async (req, res) => {
//     const errors = validationResult(req);
//     if (!errors.isEmpty()) {
//       return res.status(400).json({ errors: errors.array() });
//     }
//     try {
//       console.log("req.body", req.body);
//       const { users, date, time } = req.body;

//       let isUserHaveBundle = false;
//       let isFreeInvitation = false;
//       const invitationBy = req.user._id;

//       const user = await User.findById(invitationBy);
//       if (!user.coverImage) {
//         return res.status(400).json({
//           error: "You must have a cover image to send an invitation.",
//         });
//       }

//       const isPremiumUser = await checkUserIsPremium(invitationBy);

//       // Parse the time for comparison
//       const newTimeParts = extractTimeParts(time);
//       if (!newTimeParts) {
//         return res.status(400).json({
//           error: "Invalid time format.",
//         });
//       }

//       // Create a date object with the time for comparison
//       const newDateTime = new Date(date);
//       newDateTime.setHours(newTimeParts[0], newTimeParts[1], 0, 0);

//       // CASE 1: Check if sender has already sent or accepted an invitation for the same time
//       // Find invitations sent by the current user for the same date
//       const sentInvitations = await Invitation.find({
//         invitationBy: invitationBy,
//         date: {
//           $gte: new Date(new Date(date).setHours(0, 0, 0, 0)),
//           $lt: new Date(new Date(date).setHours(23, 59, 59, 999)),
//         },
//         status: { $in: ["Pending", "Accepted"] },
//       });

//       // Find invitations where the current user is an invitee and has accepted
//       const acceptedInvitations = await Invitation.find({
//         "users.userId": invitationBy,
//         "users.status": "Accepted",
//         date: {
//           $gte: new Date(new Date(date).setHours(0, 0, 0, 0)),
//           $lt: new Date(new Date(date).setHours(23, 59, 59, 999)),
//         },
//       });

//       // Combine both sets of invitations
//       const allUserInvitations = [...sentInvitations, ...acceptedInvitations];

//       // Check if any of these invitations are within 1 hour of the requested time
//       for (const invitation of allUserInvitations) {
//         const existingTimeParts = extractTimeParts(invitation.time);
//         if (!existingTimeParts) continue;

//         const existingDateTime = new Date(invitation.date);
//         existingDateTime.setHours(
//           existingTimeParts[0],
//           existingTimeParts[1],
//           0,
//           0
//         );

//         // Calculate difference in hours
//         const diffInHours = Math.abs((newDateTime - existingDateTime) / 36e5);

//         // If the difference is less than 1 hour, return error (Case 1)
//         if (diffInHours < 1) {
//           return res.status(409).json({
//             error:
//               "You have already sent an invitation or scheduled a meeting for this time. Please choose a different time.",
//             case: "CASE_1",
//           });
//         }
//       }

//       // First check if this is a single-user invitation
//       if (users.length == 1) {
//         const invitee = users[0];

//         // CASE 2B: Check if the invitee has sent an invitation to the current user for the same time
//         const inviteeToUserInvitation = await Invitation.findOne({
//           invitationBy: invitee.userId,
//           "users.userId": invitationBy,
//           "users.status": "Pending",
//           date: {
//             $gte: new Date(new Date(date).setHours(0, 0, 0, 0)),
//             $lt: new Date(new Date(date).setHours(23, 59, 59, 999)),
//           },
//         }).populate("invitationBy", "userName");

//         if (inviteeToUserInvitation) {
//           const existingTimeParts = extractTimeParts(
//             inviteeToUserInvitation.time
//           );
//           if (existingTimeParts) {
//             const existingDateTime = new Date(inviteeToUserInvitation.date);
//             existingDateTime.setHours(
//               existingTimeParts[0],
//               existingTimeParts[1],
//               0,
//               0
//             );

//             // Calculate difference in hours
//             const diffInHours = Math.abs(
//               (newDateTime - existingDateTime) / 36e5
//             );

//             // If the difference is less than 1 hour, return warning (Case 2b)
//             if (diffInHours < 1) {
//               const inviteeName = await User.findById(invitee.userId).select(
//                 "userName"
//               );
//               return res.status(409).json({
//                 error: `You have already sent an invitation to ${inviteeName.userName} for this time.\nIf you accept this invitation, your invitation to ${inviteeName.userName} will be automatically canceled.`,
//                 case: "CASE_2B",
//                 buttons: [
//                   "Accept Invitation",
//                   "Cancel – Decline this invitation",
//                 ],
//                 invitationId: inviteeToUserInvitation._id,
//               });
//             }
//           }
//         }
//       }

//       // CASE 2A: Check if sender has received an open invitation for the same time
//       // Find pending invitations where the current user is an invitee
//       let receivedPendingInvitationsQuery = {
//         "users.userId": invitationBy,
//         "users.status": "Pending",
//         date: {
//           $gte: new Date(new Date(date).setHours(0, 0, 0, 0)),
//           $lt: new Date(new Date(date).setHours(23, 59, 59, 999)),
//         },
//       };

//       // For single-user invitations, exclude invitations from the current invitee
//       if (users.length == 1) {
//         receivedPendingInvitationsQuery.invitationBy = { $ne: users[0].userId };
//       }

//       const receivedPendingInvitations = await Invitation.find(
//         receivedPendingInvitationsQuery
//       ).populate("invitationBy", "userName");

//       // Check if any of these invitations are within 1 hour of the requested time
//       for (const invitation of receivedPendingInvitations) {
//         const existingTimeParts = extractTimeParts(invitation.time);
//         if (!existingTimeParts) continue;

//         const existingDateTime = new Date(invitation.date);
//         existingDateTime.setHours(
//           existingTimeParts[0],
//           existingTimeParts[1],
//           0,
//           0
//         );

//         // Calculate difference in hours
//         const diffInHours = Math.abs((newDateTime - existingDateTime) / 36e5);

//         // If the difference is less than 1 hour, return warning (Case 2a)
//         if (diffInHours < 1) {
//           return res.status(409).json({
//             error:
//               "Warning – You have already received an invitation for this time. Do you still want to send the invitation?",
//             case: "CASE_2A",
//             buttons: ["Yes, send it anyway", "Cancel"],
//           });
//         }
//       }

//       // Continue with the existing checks for invitees
//       if (users.length == 1) {
//         const invitee = users[0];

//         // Check for existing invitations for the invitee
//         const existingInvitation = await Invitation.findOne({
//           "users.userId": invitee.userId,
//           date: new Date(date),
//         })
//           .sort({ createdAt: -1 })
//           .limit(1);

//         if (existingInvitation) {
//           const existingInvitationDate = existingInvitation.date
//             .toISOString()
//             .split("T")[0];
//           const newInvitationDate = new Date(date).toISOString().split("T")[0];

//           if (existingInvitationDate === newInvitationDate) {
//             const currentDate = new Date().toISOString().split("T")[0];

//             // Check if the invitation is accepted and the date is today
//             if (
//               existingInvitation.status === "Accepted" &&
//               existingInvitationDate === currentDate
//             ) {
//               const existingTimeParts = extractTimeParts(
//                 existingInvitation.time
//               );
//               console.log("existingTimeParts", existingTimeParts);
//               console.log("newTimeParts", newTimeParts);
//               if (!existingTimeParts) {
//                 return res.status(400).json({
//                   error: "Invalid time format.",
//                 });
//               }

//               // Calculate existing and new date times
//               const existingDateTime = new Date(existingInvitation.date);
//               existingDateTime.setHours(
//                 existingTimeParts[0],
//                 existingTimeParts[1]
//               );

//               // Calculate difference in hours
//               const diffInHours = Math.abs(
//                 (newDateTime - existingDateTime) / 36e5
//               );

//               // Check if the difference is less than 1 hour
//               if (diffInHours < 1) {
//                 return res.status(400).json({
//                   error:
//                     "You can only send an invitation an hour before or after the previous time on the same day.",
//                 });
//               }
//             } else if (existingInvitationDate === currentDate) {
//               // If the invitation is pending or rejected and the date is today, return error
//               if (
//                 existingInvitation.status === "Pending" ||
//                 existingInvitation.status === "Rejected"
//               ) {
//                 return res.status(400).json({
//                   error:
//                     "You have already sent an invitation for the same date which is pending or rejected.",
//                 });
//               }
//             } else {
//               // If invitation date is same but not today, return error
//               return res.status(400).json({
//                 error: "You have already sent an invitation for the same date.",
//               });
//             }
//           }
//         }
//       }
//       // Group invitation check
//       else if (users.length > 1) {
//         for (const invitee of users) {
//           const existingInvitation = await Invitation.findOne({
//             "users.userId": invitee.userId,
//             date: new Date(date),
//           })
//             .sort({ createdAt: -1 })
//             .limit(1);
//           if (existingInvitation) {
//             const existingInvitationDate = existingInvitation.date
//               .toISOString()
//               .split("T")[0];
//             const newInvitationDate = new Date(date)
//               .toISOString()
//               .split("T")[0];

//             if (existingInvitationDate === newInvitationDate) {
//               const existingTimeParts = extractTimeParts(
//                 existingInvitation.time
//               );

//               if (!existingTimeParts) {
//                 return res.status(400).json({
//                   error: "Invalid time format.",
//                 });
//               }

//               const existingDateTime = new Date(existingInvitation.date);
//               existingDateTime.setHours(
//                 existingTimeParts[0],
//                 existingTimeParts[1]
//               );

//               const diffInHours = Math.abs(
//                 (newDateTime - existingDateTime) / 36e5
//               );

//               if (diffInHours < 1) {
//                 const inviteeUser = await User.findById(invitee.userId);
//                 return res.status(400).json({
//                   error: `User ${inviteeUser.userName} has already been invited at a time close to the requested time.`,
//                 });
//               }
//             }
//           }
//         }
//       }

//       // Create feedback with value coming from frontend
//       if (!isPremiumUser) {
//         const dailyInviteCount = await checkIsDailyInviteFinish(invitationBy);
//         if (dailyInviteCount < 2) {
//           isFreeInvitation = true;
//         }
//         if (dailyInviteCount >= 2) {
//           const findBundle = await Bundle.findOne({ userId: invitationBy });
//           if (findBundle && findBundle.pendingInvitation > 0) {
//             isUserHaveBundle = true;
//           }
//         }
//       }

//       if (isFreeInvitation || isPremiumUser || isUserHaveBundle) {
//         const Feedback = await feedback.create({
//           userId: req.user._id,
//           given: false,
//           status: "no",
//         });

//         // Send notifications
//         for (let invitee of users) {
//           await UserNotification.create({
//             title: req.user.userName,
//             body: getNotificationText()["Sent_Invitation"],
//             image: req.user.coverImage ?? "N/A",
//             userId: invitee.userId,
//           });

//           const user = await User.findById(invitee.userId);

//           // if (user && user.fcmToken) {
//           //   await sendPushNotification({
//           //     title: `from ${req.user.userName}`,
//           //     body: `Hello ${user.userName}, you have received an invitation.`,
//           //     fcmToken: user.fcmToken,
//           //     userType: "USER",
//           //   });
//           // }
//         }

//         // Create the invitation
//         const finalData = { ...req.body, date: date + "Z" };
//         console.log("req.body", req.body);
//         const invitePeople = await Invitation.create({
//           ...finalData,
//           feedbackId: Feedback._id,
//           invitationBy,
//         });
//         await invitePeople.populate("users.userId feedbackId");

//         if (invitePeople) {
//           if (isUserHaveBundle) {
//             const bundelInvite = await Bundle.findOneAndUpdate(
//               { userId: invitationBy },
//               { $inc: { pendingInvitation: -1 } },
//               { new: true }
//             );
//           }

//           // Send emails after creating the invitation
//           // for (let val of invitePeople.users) {
//           //   const user = await User.findById(val.userId._id);

//           //   // Send notification email
//           //   const mailOptions = {
//           //     from: '"NETME App" <' + process.env.USER_APP_EMAIL + ">",
//           //     to: user.email,
//           //     subject: "Invitation Notification",
//           //     text: `Hey, ${req.user?.userName} has sent you an invitation`,
//           //   };
//           //   await sendEmail(mailOptions);
//           // }

//           res.status(200).json({ invitePeople });
//         } else {
//           throw Error("Something went wrong");
//         }
//       } else {
//         res.status(400).json({
//           error:
//             "Please purchase a subscription or any plan to invite more people",
//         });
//       }
//     } catch (err) {
//       console.log(err);
//       res.status(400).json({ error: "Something Went Wrong" });
//     }
//   },
// ];

// module.exports.getMyInvitations = async (req, res) => {
//   try {
//     const userId = req.user._id;
//     console.log("userId", userId);
//     const invitation = await Invitation.find({
//       invitationBy: userId,
//       deleted: false,
//     }).populate("invitationBy feedbackId businessId rescheduleBy users.userId");
//     console.log("invitation", invitation);
//     if (!invitation) {
//       return res
//         .status(404)
//         .json({ error: `Invitation not found with this user ${userId}` });
//     }
//     res.status(200).json({ invitation, total: invitation.length });
//   } catch (err) {
//     res.status(400).json({ error: "Something Went Wrong" });
//   }
// };
// module.exports.getInvitations = async (req, res) => {
//   try {
//     const userId = req.user._id;

//     // Invites RECEIVED by the user (sent by others)
//     const invitations = await Invitation.find({
//       "users.userId": userId,
//       invitationBy: { $ne: userId }, // Not sent by this user
//       deleted: false,
//     }).populate("invitationBy feedbackId businessId rescheduleBy users.userId");

//     // Open Requests SENT or RESCHEDULED by the user
//     const openRequests = await Invitation.find({
//       deleted: false,
//       $or: [
//         // Sent by this user and still active
//         { invitationBy: userId },
//         // Rescheduled by this user
//         { isRescheduled: true, rescheduleBy: userId },
//       ],
//     }).populate("invitationBy feedbackId businessId rescheduleBy users.userId");

//     res.status(200).json({
//       invitations,
//       openRequests,
//       total: invitations.length + openRequests.length,
//     });
//   } catch (err) {
//     console.error("getMyInvitations error:", err);
//     res.status(400).json({ error: "Something went wrong" });
//   }
// };

module.exports.getMyInvitations = async (req, res) => {
  try {
    const userId = req.user._id;

    const invitations = await Invitation.aggregate([
      {
        $match: {
          deleted: false,
          invitationBy: new mongoose.Types.ObjectId(userId),
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "rescheduleBy",
          foreignField: "_id",
          as: "rescheduleBy",
        },
      },
      { $unwind: { path: "$rescheduleBy", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "feedbacks",
          localField: "feedbackId",
          foreignField: "_id",
          as: "feedbackId",
        },
      },
      { $unwind: { path: "$feedbackId", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "invitationBy",
          foreignField: "_id",
          as: "invitationBy",
        },
      },
      { $unwind: { path: "$invitationBy", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "partnerbusinesses",
          localField: "businessId",
          foreignField: "_id",
          as: "businessData",
        },
      },
      { $unwind: { path: "$businessData", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$users", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "users.userId",
          foreignField: "_id",
          as: "users.userId",
        },
      },
      { $unwind: { path: "$users.userId", preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          businessId: {
            $ifNull: ["$businessData", "$googleBusiness"],
          },
        },
      },
      {
        $group: {
          _id: "$_id",
          users: { $push: "$users" },
          groupName: { $first: "$groupName" },
          dob: { $first: "$dob" },
          isSeen: { $first: "$isSeen" },
          invitationBy: { $first: "$invitationBy" },
          feedbackId: { $first: "$feedbackId" },
          businessId: { $first: "$businessId" },
          date: { $first: "$date" },
          time: { $first: "$time" },
          isGroup: { $first: "$isGroup" },
          isRescheduled: { $first: "$isRescheduled" },
          rescheduleBy: { $first: "$rescheduleBy" },
          status: { $first: "$status" },
          reschedule: { $first: "$reschedule" },
          createdAt: { $first: "$createdAt" },
          isPremium: { $first: "$invitationBy.isPremium" },
        },
      },
      {
        $addFields: {
          isPremiumRecentlySent: {
            $and: [
              { $eq: ["$isPremium", true] },
              { $gte: [{ $subtract: [new Date(), "$createdAt"] }, 0] },
              {
                $lt: [
                  { $subtract: [new Date(), "$createdAt"] },
                  24 * 60 * 60 * 1000,
                ],
              },
            ],
          },
        },
      },
      {
        $sort: {
          isPremiumRecentlySent: -1,
          createdAt: -1,
        },
      },
    ]);

    if (!invitations || invitations.length === 0) {
      return res.status(404).json({ error: "No invitations found." });
    }

    res
      .status(200)
      .json({ invitation: invitations, total: invitations.length });
  } catch (err) {
    console.error("getMyInvitations error:", err);
    res.status(400).json({ error: "Something went wrong" });
  }
};

module.exports.getInvitations = async (req, res) => {
  try {
    const userId = new mongoose.Types.ObjectId(req.user._id);

    // Common stages used in both pipelines
    const commonLookups = [
      {
        $lookup: {
          from: "users",
          localField: "rescheduleBy",
          foreignField: "_id",
          as: "rescheduleBy",
        },
      },
      { $unwind: { path: "$rescheduleBy", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "feedbacks",
          localField: "feedbackId",
          foreignField: "_id",
          as: "feedbackId",
        },
      },
      { $unwind: { path: "$feedbackId", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "invitationBy",
          foreignField: "_id",
          as: "invitationBy",
        },
      },
      { $unwind: { path: "$invitationBy", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "partnerbusinesses",
          localField: "businessId",
          foreignField: "_id",
          as: "businessData",
        },
      },
      { $unwind: { path: "$businessData", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$users", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "users.userId",
          foreignField: "_id",
          as: "users.userId",
        },
      },
      { $unwind: { path: "$users.userId", preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          businessId: {
            $ifNull: ["$businessData", "$googleBusiness"],
          },
        },
      },
      {
        $group: {
          _id: "$_id",
          users: { $push: "$users" },
          groupName: { $first: "$groupName" },
          dob: { $first: "$dob" },
          isSeen: { $first: "$isSeen" },
          invitationBy: { $first: "$invitationBy" },
          feedbackId: { $first: "$feedbackId" },
          businessId: { $first: "$businessId" },
          date: { $first: "$date" },
          time: { $first: "$time" },
          isGroup: { $first: "$isGroup" },
          isRescheduled: { $first: "$isRescheduled" },
          rescheduleBy: { $first: "$rescheduleBy" },
          status: { $first: "$status" },
          reschedule: { $first: "$reschedule" },
          createdAt: { $first: "$createdAt" },
          isPremium: { $first: "$invitationBy.isPremium" },
        },
      },
      {
        $addFields: {
          isPremiumRecentlySent: {
            $and: [
              { $eq: ["$isPremium", true] },
              { $gte: [{ $subtract: [new Date(), "$createdAt"] }, 0] },
              {
                $lt: [
                  { $subtract: [new Date(), "$createdAt"] },
                  24 * 60 * 60 * 1000,
                ],
              },
            ],
          },
        },
      },
      {
        $sort: {
          isPremiumRecentlySent: -1,
          createdAt: -1,
        },
      },
    ];

    // INVITATIONS:
    // 1. Invitations sent to me by others (I'm recipient)
    // 2. My own sent invitations
    const invitationsPipeline = [
      {
        $match: {
          deleted: false,
          $or: [
            // I'm a recipient (invitation was sent to me)
            {
              "users.userId": userId,
              invitationBy: { $ne: userId },
            },
            // I'm the sender (invitation was sent by me)
            {
              invitationBy: userId,
            },
          ],
        },
      },
      ...commonLookups,
    ];

    // OPEN REQUESTS:
    // 1. Invitations I rescheduled (originally sent to me)
    // 2. Invitations I sent that were rescheduled by recipient
    const openRequestsPipeline = [
      {
        $match: {
          deleted: false,
          $or: [
            // Invitations originally sent to me that I rescheduled
            {
              "users.userId": userId,
              isRescheduled: true,
              rescheduleBy: userId,
            },
            // Invitations I sent that were rescheduled by recipient
            {
              invitationBy: userId,
              isRescheduled: true,
              rescheduleBy: { $ne: userId },
            },
          ],
        },
      },
      ...commonLookups,
    ];

    const [invitations, openRequests] = await Promise.all([
      Invitation.aggregate(invitationsPipeline),
      Invitation.aggregate(openRequestsPipeline),
    ]);

    res.status(200).json({
      invitations,
      openRequests,
      total: invitations.length + openRequests.length,
    });
  } catch (err) {
    console.error("getInvitations error:", err);
    res.status(400).json({ error: "Something went wrong" });
  }
};

module.exports.getAllRequests = async (req, res) => {
  try {
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    let skipValue = (page - 1) * limit;
    const currentDate = new Date();
    const currentTime = `${currentDate.getHours()}:${currentDate.getMinutes()}`;

    // Start of today
    let startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);
    let obj = {};

    let aggObj = {
      date: { $gte: startOfDay },
    };

    if (req.query.status) {
      obj["status"] = req.query.status;
      aggObj["status"] = req.query.status;
    }
    if (req.query.myInvitations === "true") {
      const userConditions = [
        // { "invitationBy._id": mongoose.Types.ObjectId(req.user._id) },
        { "rescheduleBy._id": mongoose.Types.ObjectId(req.user._id) },
        // { movedToOpenRequests: true },
      ];

      obj["$or"] = userConditions;
      obj["status"] = "Pending";
      obj["status"] = "Pending";

      aggObj["$or"] = userConditions;
      aggObj["status"] = "Pending";
    }
    // if (req.query.myInvitations === "true") {
    //   obj["invitationBy"] = req.user._id;
    //   obj["status"] = "Pending";
    //   aggObj["status"] = "Pending";
    //   aggObj["invitationBy._id"] = mongoose.Types.ObjectId(req.user._id);
    // }

    const data = await getAggregatedInvitaion({ skipValue, limit, aggObj });
    if (!data || !Array.isArray(data)) {
      return res.status(200).json({
        futureInvitations: [],
        totalData: 0,
        totalPage: 0,
        perPage: limit,
        currentPage: page,
      });
    }
    const currentUser = await User.findById(req.user?._id).select("location");
    const futureInvitations = data.filter((ir) => {
      try {
        // Add distance to each user in the users array
        const currentUserCoords = currentUser?.location?.coordinates || [0, 0]; // Default to [0, 0] if no location

        // Helper function to calculate distance between two coordinates
        const calculateDistance = (userCoords) => {
          const currentUserLatLon = {
            latitude: currentUserCoords[0], // Latitude
            longitude: currentUserCoords[1], // Longitude
          };

          const userLatLon = {
            latitude: userCoords[0] || 0, // Latitude
            longitude: userCoords[1] || 0, // Longitude
          };

          const distance = haversine(currentUserLatLon, userLatLon, {
            unit: "km",
          });

          return parseFloat(distance.toFixed(2)); // Round to 2 decimal places
        };
        const usersWithDistance = ir.users.map((user) => {
          const userCoords = user.userId.location?.coordinates || [0, 0];
          //console.log("userCoords", userCoords);
          const distance = calculateDistance(userCoords);
          //console.log("user", user);
          return {
            ...user,
            userId: {
              ...user.userId,
              distance,
            },
          };
        });
        //console.log("usersWithDistance", usersWithDistance);
        // Calculate distance for invitation creator
        const invitationByCoords = ir.invitationBy.location?.coordinates || [
          0, 0,
        ];
        const invitationByDistance = calculateDistance(invitationByCoords);
        // Create invitationBy user object to add to users array
        const invitationByUser = {
          userId: {
            ...ir.invitationBy,
            distance: invitationByDistance,
          },
          status: "Pending",
          _id: ir.invitationBy._id.toString(), // Generate a unique ID based on the invitationBy user's ID
        };

        // Convert invitation to a plain object and modify it
        const responseData = ir;
        if (responseData.isGroup) {
          // If isGroup is true, add the invitationBy user to the users array
          responseData.users = [...usersWithDistance, invitationByUser];
        }
        // If isGroup is false, just return the users with distance
        else {
          responseData.users = usersWithDistance;
        }

        // Ensure that 'ir.time' exists
        if (!ir.time) {
          throw new Error("Missing time value");
        }

        // Use the extractTimeParts function to handle different time formats
        const timeParts = extractTimeParts(ir.time);
        if (!timeParts) {
          throw new Error("Invalid time format");
        }

        const invitationDateTime = new Date(ir.date);
        const [hours, minutes] = timeParts;
        invitationDateTime.setHours(hours, minutes, 0, 0); // Set hours and minutes

        // Check if the invitation date is today
        if (invitationDateTime.toDateString() === currentDate.toDateString()) {
          // If today, check the time
          const currentTimeDate = parseTimeOfDay(currentTime);
          return invitationDateTime > currentTimeDate; // Only future times for today's date
        } else {
          // Otherwise, for future dates, simply check if the invitation date is in the future
          return invitationDateTime > currentDate;
        }
      } catch (error) {
        console.error(
          `Error parsing date or time for invitation: ${ir.date} ${ir.time}`,
          error
        );
        return false; // Skip invalid entries
      }
    });

    // Filter out past invitations
    // const futureInvitations = data.filter((ir) => {
    //   try {
    //     const invitationTime = ir.time.split("(")[1].split(")")[0]; // Extract time from 'TimeOfDay(HH:MM)' format
    //     const invitationDateTime = new Date(ir.date);

    //     console.log("invitationDate before set", invitationDateTime);
    //     const [hours, minutes] = invitationTime.split(":").map(Number);
    //     invitationDateTime.setHours(hours, minutes, 0, 0); // Set hours and minutes
    //     // Check if the invitation date is today
    //     if (invitationDateTime.toDateString() === currentDate.toDateString()) {
    //       // If today, check the time
    //       const currentTimeDate = parseTimeOfDay(currentTime);
    //       return invitationDateTime > currentTimeDate; // Only future times for today's date
    //     } else {
    //       // Otherwise, for future dates, simply check if the invitation date is in the future
    //       return invitationDateTime > currentDate;
    //     }
    //   } catch (error) {
    //     console.error(
    //       `Error parsing date or time for invitation: ${ir.date} ${ir.time}`,
    //       error
    //     );
    //     return false; // Skip invalid entries
    //   }
    // });

    let count = await Invitation.find({
      deleted: false,
      ...obj,
    }).countDocuments();

    res.status(200).json({
      data: futureInvitations,
      totalData: count,
      totalPage: Math.ceil(count / limit),
      perPage: limit,
      currentPage: page,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

// module.exports.getInvitationsRequests = async (req, res) => {
//   try {
//     let page = parseInt(req.query.page ? req.query.page : 1);
//     let limit = parseInt(req.query.limit ? req.query.limit : 100);
//     let skipValue = (page - 1) * limit;
//     const { status, userId } = req.query;
//     let obj = {};
//     let aggObj = {};

//     if (status) {
//       obj["status"] = status;
//       aggObj["status"] = status;
//     }
//     if (userId) {
//       obj["users"] = {
//         $elemMatch: { userId },
//       };
//       aggObj["users.userId._id"] = mongoose.Types.ObjectId(userId);
//     }
//     const data = await getAggregatedInvitaion({ skipValue, limit, aggObj });
//     let count = await Invitation.find({
//       deleted: false,
//       ...obj,
//     }).countDocuments();

//     res.status(200).json({
//       data,
//       totalData: count,
//       totalPage: Math.ceil(count / limit),
//       perPage: limit,
//       currentPage: page,
//     });
//   } catch (err) {
//     console.log(err);
//     res.status(400).json({ error: "Something Went Wrong" });
//   }
// };

// module.exports.getInvitationsRequests = async (req, res) => {
//   try {
//     let page = parseInt(req.query.page ? req.query.page : 1);
//     let limit = parseInt(req.query.limit ? req.query.limit : 100);
//     let skipValue = (page - 1) * limit;
//     const { status, userId } = req.query;
//     let obj = {};
//     let aggObj = {};

//     if (status) {
//       obj["status"] = status;
//       aggObj["status"] = status;
//     }
//     if (userId) {
//       obj["users"] = {
//         $elemMatch: { userId },
//       };
//       aggObj["users.userId._id"] = mongoose.Types.ObjectId(userId);
//     }
//     const data = await getAggregatedInvitaion({ skipValue, limit, aggObj });
//     let count = await Invitation.find({
//       deleted: false,
//       ...obj,
//     }).countDocuments();
//     const responseData = data.map((invitation) => {
//       if (invitation.isGroup) {
//         // If isGroup is true, filter out the current user and add invitationBy to the users array
//         const filteredUsers = invitation.users.filter(
//           (user) => user.userId._id.toString() !== userId
//         );
//         return {
//           ...invitation,
//           users: [
//             ...invitation.users,
//             {
//               userId: invitation.invitationBy,
//               status: "Pending",
//               _id: "6787b2d03fc440ddf57a8f4f", // Add a unique ID for the invitationBy user
//             },
//           ],
//         };
//       } else {
//         // If isGroup is false, return the invitation as is
//         return invitation;
//       }
//     });
//     // const filteredData = data.map((invitation) => {
//     //   const filteredUsers = invitation.users.filter(
//     //     (user) => user.userId._id.toString() !== userId
//     //   );
//     //   console.log("invitation.invitationBy", invitation.invitationBy);
//     //   return {
//     //     ...invitation,
//     //     users: [
//     //       ...invitation.users,
//     //       {
//     //         userId: invitation.invitationBy,
//     //         status: "Pending",
//     //         _id: "6787b2d03fc440ddf57a8f4f",
//     //       }, // Add invitationBy as a separate object
//     //     ],
//     //   };
//     // });
//     res.status(200).json({
//       data: responseData,
//       totalData: count,
//       totalPage: Math.ceil(count / limit),
//       perPage: limit,
//       currentPage: page,
//     });
//   } catch (err) {
//     console.log(err);
//     res.status(400).json({ error: "Something Went Wrong" });
//   }
// };

module.exports.getInvitationsRequests = async (req, res) => {
  try {
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    let skipValue = (page - 1) * limit;
    const { status, userId } = req.query;
    let obj = {};
    let aggObj = {};

    if (status) {
      obj["status"] = status;
      aggObj["status"] = status;
    }
    if (userId) {
      obj["users"] = {
        $elemMatch: { userId },
      };
      aggObj["users.userId._id"] = mongoose.Types.ObjectId(userId);
    }

    // Fetch the current logged-in user's location
    const currentUser = await User.findById(userId).select("location");
    const currentUserCoords = currentUser?.location?.coordinates || [0, 0]; // Default to [0, 0] if no location

    const data = await getAggregatedInvitationsRequests({
      skipValue,
      limit,
      aggObj,
      userId,
    });
    let count = await Invitation.find({
      deleted: false,
      ...obj,
    }).countDocuments();

    // Add distance to each user in the users array
    const responseData = data.map((invitation) => {
      const usersWithDistance = invitation.users.map((user) => {
        const userCoords = user.userId.location?.coordinates || [0, 0]; // Default to [0, 0] if no location
        // Ensure coordinates are in [latitude, longitude] order for haversine
        const currentUserLatLon = {
          latitude: currentUserCoords[0], // Latitude
          longitude: currentUserCoords[1], // Longitude
        };
        const userLatLon = {
          latitude: userCoords[0], // Latitude
          longitude: userCoords[1], // Longitude
        };
        const distance = haversine(currentUserLatLon, userLatLon, {
          unit: "km",
        }); // Distance in kilometers

        return {
          ...user,
          userId: {
            ...user.userId,
            distance: parseFloat(distance.toFixed(2)), // Round to 2 decimal places
          },
        };
      });
      if (invitation.isGroup) {
        const inviteUserLatLon = {
          latitude: invitation.invitationBy.location?.coordinates[0], // Latitude
          longitude: invitation.invitationBy.location?.coordinates[1], // Longitude
        };
        const currentUserLatLon = {
          latitude: currentUserCoords[0], // Latitude
          longitude: currentUserCoords[1], // Longitude
        };
        const inviteUserDistance = haversine(
          currentUserLatLon,
          inviteUserLatLon,
          {
            unit: "km",
          }
        );
        // If isGroup is true, add invitationBy to the users array
        // Note: We're not using the filtered users here, just adding the invitationBy to all users
        return {
          ...invitation,
          users: [
            ...usersWithDistance,
            {
              userId: {
                ...invitation.invitationBy,
                distance: inviteUserDistance, // Distance from the current user to themselves is 0
              },
              status: "Pending",
              _id: "6787b2d03fc440ddf57a8f4f", // Add a unique ID for the invitationBy user
            },
          ],
        };
      } else {
        // If isGroup is false, return the invitation with updated users array (including distance)
        return {
          ...invitation,
          users: usersWithDistance,
        };
      }
    });

    res.status(200).json({
      data: responseData,
      totalData: count,
      totalPage: Math.ceil(count / limit),
      perPage: limit,
      currentPage: page,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

module.exports.getInvitationById = async (req, res) => {
  try {
    const { _id, userId } = req.query;

    if (!_id) {
      return res.status(400).json({ error: "Invitation ID is required" });
    }

    // Fetch the invitation with all populated fields
    const invitation = await Invitation.findById({ _id })
      .populate("rescheduleBy")
      .populate("feedbackId")
      .populate("invitationBy")
      .populate("businessId")
      .populate("users.userId");

    if (!invitation) {
      return res.status(404).json({ error: "Invitation not found" });
    }

    // Fetch the current user's location for distance calculation
    const currentUser = await User.findById(userId || req.user?._id).select(
      "location"
    );
    const currentUserCoords = currentUser?.location?.coordinates || [0, 0]; // Default to [0, 0] if no location

    // Helper function to calculate distance between two coordinates
    const calculateDistance = (userCoords) => {
      const currentUserLatLon = {
        latitude: currentUserCoords[0], // Latitude
        longitude: currentUserCoords[1], // Longitude
      };

      const userLatLon = {
        latitude: userCoords[0] || 0, // Latitude
        longitude: userCoords[1] || 0, // Longitude
      };

      const distance = haversine(currentUserLatLon, userLatLon, {
        unit: "km",
      });

      return parseFloat(distance.toFixed(2)); // Round to 2 decimal places
    };

    // Add distance to each user in the users array
    const usersWithDistance = invitation.users.map((user) => {
      const userCoords = user.userId.location?.coordinates || [0, 0];
      const distance = calculateDistance(userCoords);

      return {
        ...user.toObject(),
        userId: {
          ...user.userId.toObject(),
          distance,
        },
      };
    });

    // Calculate distance for invitation creator
    const invitationByCoords = invitation.invitationBy.location
      ?.coordinates || [0, 0];
    const invitationByDistance = calculateDistance(invitationByCoords);

    // Create invitationBy user object to add to users array
    const invitationByUser = {
      userId: {
        ...invitation.invitationBy.toObject(),
        distance: invitationByDistance,
      },
      status: "Pending",
      _id: invitation.invitationBy._id.toString(), // Generate a unique ID based on the invitationBy user's ID
    };

    // Convert invitation to a plain object and modify it
    const responseData = invitation.toObject();
    if (responseData.isGroup) {
      // If isGroup is true, add the invitationBy user to the users array
      responseData.users = [...usersWithDistance, invitationByUser];
    }
    // If isGroup is false, just return the users with distance
    else {
      responseData.users = usersWithDistance;
    }
    // responseData.users = [...usersWithDistance, invitationByUser];

    res.status(200).json({
      data: responseData,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};
module.exports.getInvitationByLastAccepted = async (req, res) => {
  try {
    const { user1Id, user2Id } = req.query;

    // Check if both user IDs are provided
    if (!user1Id || !user2Id) {
      return res.status(400).json({ error: "Both user IDs are required" });
    }

    // Find the latest invitation where both users are involved and at least one has accepted
    const latestInvitation = await Invitation.findOne({
      $or: [
        // Case 1: user1 is the invitation creator and user2 is in users array
        {
          invitationBy: user1Id,
          "users.userId": user2Id,
          "users.status": "Accepted",
        },
        // Case 2: user2 is the invitation creator and user1 is in users array
        {
          invitationBy: user2Id,
          "users.userId": user1Id,
          "users.status": "Accepted",
        },
      ],
    })
      .populate("rescheduleBy")
      .populate("feedbackId")
      .populate("invitationBy")
      .populate("businessId")
      .populate("users.userId")
      .sort({ updatedAt: -1 }) // Sort by most recent first
      .limit(1); // Get only the latest one

    if (!latestInvitation) {
      return res
        .status(404)
        .json({ error: "No accepted invitations found between these users" });
    }

    // Fetch the current user's location for distance calculation
    const currentUser = await User.findById(req.user?._id).select("location");
    const currentUserCoords = currentUser?.location?.coordinates || [0, 0]; // Default to [0, 0] if no location

    // Helper function to calculate distance between two coordinates
    const calculateDistance = (userCoords) => {
      const currentUserLatLon = {
        latitude: currentUserCoords[0], // Latitude
        longitude: currentUserCoords[1], // Longitude
      };

      const userLatLon = {
        latitude: userCoords[0] || 0, // Latitude
        longitude: userCoords[1] || 0, // Longitude
      };

      const distance = haversine(currentUserLatLon, userLatLon, {
        unit: "km",
      });

      return parseFloat(distance.toFixed(2)); // Round to 2 decimal places
    };

    // Add distance to each user in the users array
    const usersWithDistance = latestInvitation.users.map((user) => {
      const userCoords = user.userId?.location?.coordinates || [0, 0];
      const distance = calculateDistance(userCoords);

      // Check if toObject method exists and use it, otherwise use the object as is
      const userObj =
        typeof user.toObject === "function" ? user.toObject() : user;
      const userIdObj =
        typeof user.userId?.toObject === "function"
          ? user.userId.toObject()
          : user.userId;

      return {
        ...userObj,
        userId: {
          ...userIdObj,
          distance,
        },
      };
    });

    // Calculate distance for invitation creator
    const invitationByCoords = latestInvitation.invitationBy?.location
      ?.coordinates || [0, 0];
    const invitationByDistance = calculateDistance(invitationByCoords);

    // Create invitationBy user object to add to users array
    const invitationByObj =
      typeof latestInvitation.invitationBy?.toObject === "function"
        ? latestInvitation.invitationBy.toObject()
        : latestInvitation.invitationBy;

    const invitationByUser = {
      userId: {
        ...invitationByObj,
        distance: invitationByDistance,
      },
      status: "Pending",
      _id: latestInvitation.invitationBy._id.toString(),
    };

    // Convert invitation to a plain object and modify it
    const responseData =
      typeof latestInvitation.toObject === "function"
        ? latestInvitation.toObject()
        : latestInvitation;

    if (responseData.isGroup) {
      // If isGroup is true, add the invitationBy user to the users array
      responseData.users = [...usersWithDistance, invitationByUser];
    } else {
      // If isGroup is false, just return the users with distance
      responseData.users = usersWithDistance;
    }

    res.status(200).json({
      data: responseData,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

module.exports.updateInvitation = [
  body("invitationId").notEmpty().withMessage("invitationId is required"),
  body("status")
    .optional()
    .isIn(["Pending", "Accepted", "Rejected"])
    .withMessage("Invalid status"),
  body("reschedule.date")
    .if(body("status").equals("Pending"))
    .isISO8601()
    .withMessage("Valid reschedule date is required"),
  body("reschedule.time")
    .if(body("status").equals("Pending"))
    .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage("Valid reschedule time (HH:MM) is required"),
  body("deleted")
    .optional()
    .isBoolean()
    .withMessage("Deleted must be a boolean"),
  body("movedToOpenRequests")
    .optional()
    .isBoolean()
    .withMessage("movedToOpenRequests must be a boolean"),
  body("isRemoveOtherInvitation")
    .optional()
    .isBoolean()
    .withMessage("isRemoveOtherInvitation must be a boolean"),

  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const {
        invitationId,
        status,
        reschedule,
        deleted,
        isRemoveOtherInvitation,
      } = req.body;
      const userId = req.user._id;

      // Handle deletion
      if (deleted === true) {
        // First check if invitation exists and user has permission
        const invitationToDelete = await Invitation.findById(invitationId);

        if (!invitationToDelete) {
          return res.status(404).json({ error: "Invitation not found" });
        }

        // Only allow deletion by the invitation creator
        if (!invitationToDelete.invitationBy.equals(userId)) {
          return res.status(403).json({
            error: "Only the invitation creator can delete the invitation",
            case: "UNAUTHORIZED_DELETE",
          });
        }

        // Allow deletion of pending invitations or failed group invitations
        if (
          invitationToDelete.status !== "Pending" &&
          !(
            invitationToDelete.isGroup && invitationToDelete.status === "Failed"
          )
        ) {
          return res.status(400).json({
            error:
              "Only pending invitations or failed group invitations can be deleted",
            case: "INVALID_STATUS_FOR_DELETE",
          });
        }

        const deletedInvitation = await Invitation.findByIdAndUpdate(
          invitationId,
          { deleted: true },
          { new: true }
        );

        // Delete associated chat for group invitations
        if (!!deletedInvitation.isGroup) {
          await Chat.findOneAndUpdate(
            { invitationId: invitationId },
            { deleted: true },
            { new: true }
          );
        }

        // Notify participants
        await Promise.all(
          deletedInvitation.users.map(async (user) => {
            if (user.userId.toString() !== userId.toString()) {
              await UserNotification.create({
                title: req.user.userName,
                body: `${req.user.userName} has withdrawn the invitation`,
                image: req.user.coverImage,
                userId: user.userId,
              });
            }
          })
        );

        return res
          .status(200)
          .json({ message: "Invitation deleted successfully" });
      }

      const invitation = await Invitation.findOne({
        _id: invitationId,
        deleted: { $ne: true },
      }).populate("invitationBy", "userName");

      // Accept rescheduled invitation by sender
      if (!!invitation.isRescheduled && status === "Accepted") {
        console.log(
          "invitation.rescheduleBy.toString()",
          invitation.rescheduleBy.toString()
        );
        const updateObj = {
          $set: {
            "users.$[elem].status": "Accepted",
            status: "Accepted",
          },
        };
        await Invitation.findOneAndUpdate({ _id: invitationId }, updateObj, {
          new: true,
          arrayFilters: [{ "elem.userId": invitation.rescheduleBy.toString() }],
        });
      }
      if (!invitation) {
        return res
          .status(404)
          .json({ error: "Invitation not found or has been withdrawn" });
      }

      // Check authorization (keep existing logic)
      const userInInvitation = invitation.users.find((u) =>
        u.userId.equals(userId)
      );

      if (!userInInvitation && !invitation.invitationBy.equals(userId)) {
        return res.status(403).json({
          error: "You are not authorized to update this invitation",
          case: "UNAUTHORIZED",
        });
      }
      //console.log("userId", userId);
      //console.log(
      //  "invitation.invitationBy.equals(userId)",
      //  invitation.invitationBy.equals(userId)
      //);

      // if (!userInInvitation && !invitation.invitationBy.equals(userId)) {
      //   return res
      //     .status(403)
      //     .json({ error: "Not authorized to update this invitation" });
      // }

      // NEW: Check for conflicting accepted invitations when accepting
      // if (status === "Accepted") {
      //   const conflictCheck = await checkForConflictingAcceptedInvitations(
      //     userId,
      //     invitation
      //   );
      //   if (conflictCheck.hasConflict) {
      //     return res.status(409).json({
      //       error: `Warning: You have already accepted an invitation from ${conflictCheck.conflictingUser} for this time. Accepting this will cancel the previous one.`,
      //       case: "ACCEPTED_CONFLICT",
      //       buttons: ["Accept Anyway", "Cancel"],
      //       conflictingInvitationId: conflictCheck.conflictingInvitationId,
      //     });
      //   }
      // }

      let updateObj = {};
      let notificationStatus;
      let isReschedule = false;

      // Initialize Socket.IO service for status messages and direct MongoDB access
      const socketService = require("../../services/socketService");
      let rescheduleDateTime;
      switch (status) {
        case "Accepted":
          // Handle conflicting invitations within ±3 hours
          const invitationDateTime = new Date(invitation.date);
          const timeParts = extractTimeParts(invitation.time);

          if (timeParts) {
            invitationDateTime.setHours(timeParts[0], timeParts[1], 0, 0);

            const startOfDay = new Date(invitation.date);
            startOfDay.setHours(0, 0, 0, 0);

            const endOfDay = new Date(invitation.date);
            endOfDay.setHours(23, 59, 59, 999);

            // Define the 3-hour window around the accepted invitation
            const threeHoursBefore = new Date(invitationDateTime);
            threeHoursBefore.setHours(invitationDateTime.getHours() - 3);

            const threeHoursAfter = new Date(invitationDateTime);
            threeHoursAfter.setHours(invitationDateTime.getHours() + 3);

            // Find all pending invitations for the same day where user is either sender or recipient
            const conflictingInvitations = await Invitation.find({
              $or: [
                // User is the sender
                { invitationBy: userId },
                // User is a recipient
                { "users.userId": userId },
              ],
              date: { $gte: startOfDay, $lt: endOfDay },
              status: "Pending",
              deleted: { $ne: true },
              _id: { $ne: invitationId },
            }).populate("invitationBy", "userName");
            console.log("conflictingInvitations", conflictingInvitations);

            // Filter to only include invitations within the 3-hour window
            const conflictsWithin3Hours = [];
            console.log("conflictsWithin3Hours", conflictsWithin3Hours);
            for (const conflictInv of conflictingInvitations) {
              const conflictTimeParts = extractTimeParts(conflictInv.time);
              console.log("conflictTimeParts", conflictTimeParts);
              if (conflictTimeParts) {
                const conflictDateTime = new Date(conflictInv.date);
                conflictDateTime.setHours(
                  conflictTimeParts[0],
                  conflictTimeParts[1],
                  0,
                  0
                );
                console.log("conflictDateTime", conflictDateTime);
                // Check if the invitation falls within the 3-hour window
                if (
                  conflictDateTime >= threeHoursBefore &&
                  conflictDateTime <= threeHoursAfter
                ) {
                  conflictsWithin3Hours.push({
                    invitation: conflictInv,
                    dateTime: conflictDateTime,
                  });
                }
              }
            }

            // If there are conflicts and user hasn't confirmed removal, return warning
            if (conflictsWithin3Hours.length > 0 && !isRemoveOtherInvitation) {
              // Format conflict information for the response
              const conflictDetails = conflictsWithin3Hours.map((conflict) => {
                const inv = conflict.invitation;
                const dateTime = conflict.dateTime;
                const formattedTime = dateTime.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: false,
                });
                const formattedDate = dateTime.toLocaleDateString();

                // Determine if user is sender or recipient
                const isSender =
                  inv.invitationBy.toString() === userId.toString();
                const otherParty = isSender
                  ? inv.users[0]?.userId
                    ? `invitation to ${inv.users[0].userName || "another user"}`
                    : "an invitation"
                  : `invitation from ${
                      inv.invitationBy.userName || "another user"
                    }`;

                return `${otherParty} on ${formattedDate} at ${formattedTime}`;
              });
              console.log("conflictDetails", conflictDetails);
              return res.status(409).json({
                error: `You have ${conflictsWithin3Hours.length} conflicting invitation(s) within ±3 hours of this time. Accepting this invitation will reject these conflicts:`,
                conflictDetails: conflictDetails,
                case: "CONFLICTING_INVITATIONS",
                buttons: ["Accept and Remove Conflicts", "Cancel"],
                conflictCount: conflictsWithin3Hours.length,
              });
            }

            // If user confirmed removal or there are no conflicts, proceed with rejection
            if (isRemoveOtherInvitation && conflictsWithin3Hours.length > 0) {
              // Track rejected invitations for notification
              const rejectedInvitations = [];

              await Promise.all(
                conflictsWithin3Hours.map(async (conflict) => {
                  const conflictInv = conflict.invitation;
                  console.log("conflictInv", conflictInv);
                  // Mark this invitation as rejected
                  await Invitation.findByIdAndUpdate(conflictInv._id, {
                    status: "Rejected",
                    deleted: true,
                    rejectionReason:
                      "Automatically rejected due to conflicting accepted invitation",
                  });

                  rejectedInvitations.push(conflictInv);
                })
              );
              console.log("rejectedInvitations", rejectedInvitations);

              // Send notifications for all rejected invitations
              for (const rejectedInv of rejectedInvitations) {
                // If user is the sender of the rejected invitation, notify all recipients
                if (rejectedInv.invitationBy.toString() === userId.toString()) {
                  await Promise.all(
                    rejectedInv.users.map(async (user) => {
                      await UserNotification.create({
                        title: req.user.userName,
                        body: `${req.user.userName} has canceled the invitation due to a conflicting accepted invitation`,
                        image: req.user.coverImage,
                        userId: user.userId,
                      });
                    })
                  );
                }
                // If user is a recipient of the rejected invitation, notify the sender
                else {
                  await UserNotification.create({
                    title: req.user.userName,
                    body: `${req.user.userName} has rejected your invitation due to a conflicting accepted invitation`,
                    image: req.user.coverImage,
                    userId: rejectedInv.invitationBy,
                  });
                }
              }

              // Log the number of automatically rejected invitations
              console.log(
                `Automatically rejected ${rejectedInvitations.length} conflicting invitations within ±3 hours`
              );
            }
          }

          if (invitation.isGroup) {
            updateObj = {
              $set: {
                "users.$[elem].status": "Accepted",
              },
            };
            const updatedInvitation = await Invitation.findOneAndUpdate(
              { _id: invitationId },
              updateObj,
              {
                new: true,
                arrayFilters: [{ "elem.userId": userId }],
              }
            );
            // Check if all users have accepted
            const allAccepted = updatedInvitation.users.every(
              (user) => user.status === "Accepted"
            );

            if (allAccepted) {
              await Invitation.findByIdAndUpdate(invitationId, {
                $set: { status: "Accepted" },
              });
            }
          } else {
            updateObj = {
              $set: {
                "users.$[elem].status": "Accepted",
                status: "Accepted",
              },
            };
            await Invitation.findOneAndUpdate(
              { _id: invitationId },
              updateObj,
              {
                new: true,
                arrayFilters: [{ "elem.userId": userId }],
              }
            );
          }

          // updateObj = { status: "Accepted" };
          notificationStatus = "accepted";

          // Send status update message for acceptance
          try {
            console.log("invitation.invitationBy", invitation.invitationBy);
            await socketService.sendInvitationResponseMessages(
              invitationId,
              "accepted",
              userId.toString(),
              invitation.invitationBy,
              invitation.isGroup || false
            );
          } catch (msgError) {
            console.error("Error sending acceptance status message:", msgError);
            // Continue with the process even if message sending fails
          }
          break;

        case "Rejected":
          if (invitation.isGroup) {
            updateObj = {
              $set: {
                "users.$[elem].status": "Rejected",
              },
            };

            const updatedInvitation = await Invitation.findOneAndUpdate(
              { _id: invitationId },
              updateObj,
              {
                new: true,
                arrayFilters: [{ "elem.userId": userId }],
              }
            );

            // Check if all users have rejected
            const allRejected = updatedInvitation.users.every(
              (user) => user.status === "Rejected"
            );

            if (allRejected) {
              await Invitation.findByIdAndUpdate(invitationId, {
                $set: { status: "Rejected" },
              });
            }
          } else {
            updateObj = {
              $set: {
                "users.$[elem].status": "Rejected",
                status: "Rejected",
              },
            };
          }
          updateObj = { status: "Rejected" };
          notificationStatus = "rejected";
          console.log("invitation.invitationBy", invitation.invitationBy);
          // Send status update message for rejection
          try {
            await socketService.sendInvitationResponseMessages(
              invitationId,
              "declined",
              userId.toString(),
              invitation.invitationBy,
              invitation.isGroup || false
            );
          } catch (msgError) {
            console.error("Error sending rejection status message:", msgError);
            // Continue with the process even if message sending fails
          }
          break;
        case "Pending":
          // Reschedule logic
          if (!reschedule || !reschedule.date || !reschedule.time) {
            return res
              .status(400)
              .json({ error: "Reschedule date and time are required" });
          }

          // Validate reschedule time
          const rescheduleTimeParts = extractTimeParts(reschedule.time);
          if (!rescheduleTimeParts) {
            return res
              .status(400)
              .json({ error: "Invalid reschedule time format" });
          }

          // Check if user has already used their reschedule
          if (
            invitation.rescheduleBy &&
            invitation.rescheduleBy.equals(userId)
          ) {
            return res.status(400).json({
              error: "You have already used your one-time reschedule option",
              buttons: ["Accept", "Reject"],
            });
          }

          rescheduleDateTime = new Date(
            `${reschedule.date}T${reschedule.time}:00Z`
          );
          if (isNaN(rescheduleDateTime.getTime())) {
            return res
              .status(400)
              .json({ error: "Invalid reschedule date/time" });
          }

          // Check for conflicts with the new time - including blocked period conflicts
          const blockedPeriodCheck = await checkForBlockedPeriodConflicts(
            userId,
            rescheduleDateTime
          );

          if (blockedPeriodCheck.hasConflict) {
            const conflictTime =
              blockedPeriodCheck.conflictingDateTime.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
                hour12: false,
              });
            const conflictDate =
              blockedPeriodCheck.conflictingDateTime.toLocaleDateString();

            return res.status(409).json({
              error: `You cannot reschedule to this time because it conflicts with the blocked period of your existing invitation with ${blockedPeriodCheck.conflictingUserName} on ${conflictDate} at ${conflictTime}. Please select a time that is at least 3 hours before or after your existing invitation.`,
              case: "RESCHEDULE_BLOCKED_PERIOD_CONFLICT",
            });
          }

          const startOfDay = new Date(reschedule.date);
          startOfDay.setHours(0, 0, 0, 0);

          const endOfDay = new Date(reschedule.date);
          endOfDay.setHours(23, 59, 59, 999);

          // Check for active invitations only (exclude completed ones with feedback)
          const userInvitations = await Invitation.find({
            $or: [
              {
                invitationBy: userId,
                date: { $gte: startOfDay, $lt: endOfDay },
              },
              {
                "users.userId": userId,
                date: { $gte: startOfDay, $lt: endOfDay },
              },
            ],
            status: { $in: ["Pending", "Accepted"] },
            deleted: { $ne: true },
            _id: { $ne: invitationId },
          }).populate("feedbackId");

          // Filter out completed invitations
          const activeUserInvitations = userInvitations.filter((inv) => {
            return !inv.feedbackId || !inv.feedbackId.given;
          });

          for (const inv of activeUserInvitations) {
            const invTimeParts = extractTimeParts(inv.time);
            if (invTimeParts) {
              const invDateTime = new Date(inv.date);
              invDateTime.setHours(invTimeParts[0], invTimeParts[1], 0, 0);

              const diffInHours = Math.abs(
                (rescheduleDateTime - invDateTime) / 36e5
              );

              if (diffInHours < 3) {
                return res.status(409).json({
                  error:
                    "You already have another invitation scheduled at this time. Please choose a different time to reschedule. Please select a time that is at least 3 hours before or after your existing invitation.",
                  case: "RESCHEDULE_CONFLICT",
                });
              }
            }
          }

          updateObj = {
            $set: {
              isRescheduled: true,
              rescheduleBy: userId,
              reschedule: {
                date: reschedule.date,
                time: reschedule.time,
              },
              date: rescheduleDateTime,
              time: reschedule.time,
              status: "Pending",
            },
            $setOnInsert: {
              "users.$[].status": "Pending",
            },
          };
          // ENHANCED RESCHEDULE LOGIC
          const rescheduleCount = await getRescheduleCount(invitation);
          const isSender = invitation.invitationBy.equals(userId);

          // Check reschedule limits
          if (rescheduleCount >= 2) {
            return res.status(400).json({
              error: "Maximum reschedules reached for this invitation",
              buttons: ["Accept", "Reject"],
            });
          }

          // Check if this user can reschedule
          if (rescheduleCount === 1 && isSender) {
            // Second reschedule by sender - move to open requests
            updateObj = {
              $set: {
                isRescheduled: true,
                rescheduleBy: userId,
                rescheduleCount: 2,
                reschedule: {
                  date: reschedule.date,
                  time: reschedule.time,
                },
                date: rescheduleDateTime,
                time: reschedule.time,
                status: "Pending",
                movedToOpenRequests: true, // Flag for frontend
              },
              $setOnInsert: {
                "users.$[].status": "Pending",
              },
            };
          } else if (
            rescheduleCount === 0 ||
            (rescheduleCount === 1 && !isSender)
          ) {
            // First reschedule or second by receiver
            updateObj = {
              $set: {
                isRescheduled: true,
                rescheduleBy: userId,
                rescheduleCount: rescheduleCount + 1,
                reschedule: {
                  date: reschedule.date,
                  time: reschedule.time,
                },
                date: rescheduleDateTime,
                time: reschedule.time,
                status: "Pending",
              },
              $setOnInsert: {
                "users.$[].status": "Pending",
              },
            };
          }

          notificationStatus = "Rescheduled";
          isReschedule = true;
          console.log("invitation.invitationBy", invitation.invitationBy);
          // Send status update message for rescheduling
          try {
            await socketService.sendInvitationResponseMessages(
              invitationId,
              "rescheduled",
              userId.toString(),
              invitation.invitationBy,
              invitation.isGroup || false
            );
          } catch (msgError) {
            console.error("Error sending reschedule status message:", msgError);
            // Continue with the process even if message sending fails
          }
          break;
      }

      // Apply updates
      const updatedInvitation = await Invitation.findOneAndUpdate(
        { _id: invitationId },
        updateObj,
        {
          new: true,
          arrayFilters: [{ "elem.userId": userId }],
        }
      ).populate("users.userId invitationBy");

      if (!updatedInvitation) {
        return res.status(404).json({ error: "Failed to update invitation" });
      }

      // Create notification
      let notificationMessage = "";
      const isGroup = updatedInvitation.isGroup;
      const invitationOwner = updatedInvitation.invitationBy;

      if (notificationStatus === "Accepted") {
        notificationMessage = `${req.user.userName} has accepted ${
          isGroup ? "the group invitation" : "your invitation"
        }`;
      } else if (notificationStatus === "Rejected") {
        notificationMessage = `${req.user.userName} has rejected ${
          isGroup ? "the group invitation" : "your invitation"
        }`;
      } else if (notificationStatus === "Rescheduled") {
        const date = rescheduleDateTime;
        const formattedDate = date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
        notificationMessage = `${req.user.userName} has rescheduled ${
          isGroup ? "the group invitation" : "your invitation"
        } to ${formattedDate} at ${reschedule.time}`;
      }

      // Notify invitation owner
      await UserNotification.create({
        title: req.user.userName,
        body: notificationMessage,
        image: req.user.coverImage,
        userId: invitationOwner._id,
      });

      // Send email to owner
      await sendEmail({
        from: `"NETME App" <${process.env.USER_APP_EMAIL}>`,
        to: invitationOwner.email,
        subject: "Invitation Update Notification",
        text: `Hey,\n${notificationMessage}`,
      });

      // For group invitations, notify all members about reschedule
      if (isReschedule && isGroup) {
        await Promise.all(
          updatedInvitation.users.map(async (user) => {
            if (!user.userId.equals(userId)) {
              await UserNotification.create({
                title: req.user.userName,
                body: notificationMessage,
                image: req.user.coverImage,
                userId: user.userId,
              });

              const userDoc = await User.findById(user.userId);
              await sendEmail({
                from: `"NETME App" <${process.env.USER_APP_EMAIL}>`,
                to: userDoc.email,
                subject: "Group Invitation Update",
                text: `Hey,\n${notificationMessage}`,
              });
            }
          })
        );
      }

      res.status(200).json({ invitation: updatedInvitation });
    } catch (err) {
      console.error("Error in updateInvitation:", err);
      res.status(500).json({
        error: "Failed to update invitation",
        details:
          process.env.NODE_ENV === "development" ? err.message : undefined,
      });
    }
  },
];

//       if (!invitation) {
//         return res.status(404).json({ error: "Invitation not found" });
//       }

//       // Handle Accept/Decline
//       if (status) {
//         // Find the user in the invitation's users array
//         const userInInvitation = invitation.users.find(
//           (u) => u.userId.toString() === userId.toString()
//         );

//         if (!userInInvitation) {
//           return res
//             .status(404)
//             .json({ error: "User not found in invitation" });
//         }

//         // Update the user's status
//         userInInvitation.status = status;

//         // Save the updated invitation using Mongoose
//         await invitation.save();

//         // Send notification and email if the status is "Accepted"
//         if (status === "Accepted") {
//           const user = await User.findById(userId);
//           if (user) {
//             await UserNotification.create({
//               title: req.user.userName,
//               body: getNotificationText()[status + "_Invitation"],
//               image: req.user.coverImage ?? "N/A",
//               userId: userId,
//             });

//             const mailOptions = {
//               from: '"NETME App" <' + process.env.USER_APP_EMAIL + ">",
//               to: user.email,
//               subject: "Invitation Notification",
//               text: `Hey,\n ${req.user.userName} has ${
//                 getNotificationText()[status]
//               }`,
//             };
//             await sendEmail(mailOptions);
//           }
//         }

//         return res
//           .status(200)
//           .json({ message: `Invitation ${status}`, invitation });
//       }

//       // Handle Reschedule
//       if (req.body.isRescheduled) {
//         if (
//           !req.body.rescheduleBy ||
//           !req.body.reschedule ||
//           !req.body.reschedule.date ||
//           !req.body.reschedule.time
//         ) {
//           return res
//             .status(400)
//             .json({ error: "Reschedule details are required" });
//         }

//         // Update reschedule details using Mongoose
//         // invitation.isRescheduled = true;
//         // invitation.rescheduleBy = rescheduleBy;
//         // invitation.reschedule = {
//         //   date: reschedule.date,
//         //   time: reschedule.time,
//         // };

//         // // Save the updated invitation
//         // await invitation.save();
//         console.log("req.body", req.body);
//         const invitePeople = await Invitation.findOneAndUpdate(
//           { _id: req.body.invitationId },
//           {
//             ...req.body,
//           },
//           {
//             new: true,
//           }
//         );
//         return res
//           .status(200)
//           .json({ message: "Invitation rescheduled", invitePeople });
//       }

//       // If no valid action is provided
//       return res.status(400).json({ error: "No valid action provided" });
//     } catch (err) {
//       console.error(err);
//       res.status(500).json({ error: "Something Went Wrong" });
//     }
//   },
// ];

// Helper function to get current time in HH:MM format
// Currently not used but kept for potential future use
// function getCurrentTime(date) {
//   return `${date.getHours()}:${date.getMinutes()}`;
// }

function parseTimeOfDay(timeString) {
  const [hours, minutes] = timeString.split(":").map(Number);
  const now = new Date();
  now.setHours(hours, minutes, 0, 0);
  return now;
}
// NEW HELPER FUNCTIONS

// async function checkForConflictingAcceptedInvitations(
//   userId,
//   currentInvitation
// ) {
//   const currentTimeParts = extractTimeParts(currentInvitation.time);
//   if (!currentTimeParts) return { hasConflict: false };

//   const currentDateTime = new Date(currentInvitation.date);
//   currentDateTime.setHours(currentTimeParts[0], currentTimeParts[1], 0, 0);

//   const startOfDay = new Date(currentInvitation.date);
//   startOfDay.setHours(0, 0, 0, 0);

//   const endOfDay = new Date(currentInvitation.date);
//   endOfDay.setHours(23, 59, 59, 999);

//   // Find accepted invitations where user is invitee
//   const acceptedInvitations = await Invitation.find({
//     "users.userId": userId,
//     "users.status": "Accepted",
//     status: "Accepted",
//     date: { $gte: startOfDay, $lt: endOfDay },
//     _id: { $ne: currentInvitation._id },
//     deleted: { $ne: true },
//   }).populate("invitationBy", "userName");

//   for (const inv of acceptedInvitations) {
//     const invTimeParts = extractTimeParts(inv.time);
//     if (invTimeParts) {
//       const invDateTime = new Date(inv.date);
//       invDateTime.setHours(invTimeParts[0], invTimeParts[1], 0, 0);

//       const diffInHours = Math.abs((currentDateTime - invDateTime) / 36e5);
//       if (diffInHours < 1) {
//         return {
//           hasConflict: true,
//           conflictingUser: inv.invitationBy.userName,
//           conflictingInvitationId: inv._id,
//         };
//       }
//     }
//   }

//   return { hasConflict: false };
// }

/**
 * Check if a new invitation time conflicts with the blocked period of any active invitation
 * Blocked period is defined as 3 hours before and 3 hours after the existing invitation time
 * @param {String} userId - User ID to check for conflicts
 * @param {Date} newDateTime - The proposed new invitation datetime
 * @returns {Promise<Object>} - Object with conflict information
 */
async function checkForBlockedPeriodConflicts(userId, newDateTime) {
  // Only consider current and future invitations
  const now = new Date();

  // Get all active invitations (Accepted or Pending) for this user
  const activeInvitations = await Invitation.find({
    $or: [
      // User is the inviter
      { invitationBy: userId },
      // User is an invitee
      { "users.userId": userId },
    ],
    status: { $in: ["Accepted", "Pending"] },
    deleted: { $ne: true },
    // Only consider current and future invitations
    date: { $gte: now },
  })
    .populate("invitationBy", "userName")
    .populate("feedbackId");

  // Filter out completed invitations (those with feedback given)
  const trulyActiveInvitations = activeInvitations.filter((inv) => {
    return !inv.feedbackId || !inv.feedbackId.given;
  });

  for (const invitation of trulyActiveInvitations) {
    const timeParts = extractTimeParts(invitation.time);
    if (!timeParts) continue;
    console.log("timeParts", timeParts);
    console.log("invitation.date", invitation.date);
    // const invitationDateTime = new Date(invitation.date);
    // invitationDateTime.setHours(timeParts[0], timeParts[1], 0, 0);
    // console.log("invitationDateTime", invitationDateTime);
    // Calculate the blocked period (3 hours before and 3 hours after)
    const blockedStart = new Date(invitation.date);
    blockedStart.setHours(invitation.date.getHours() - 3);

    const blockedEnd = new Date(invitation.date);
    blockedEnd.setHours(invitation.date.getHours() + 3);

    // If the blocked period has already ended (more than 3 hours after the meeting), allow new invitations
    const now = new Date();
    if (blockedEnd < now) {
      continue; // Skip this invitation as its blocked period has passed
    }

    // Check if new datetime falls within the blocked period
    if (newDateTime >= blockedStart && newDateTime <= blockedEnd) {
      // Get the other user's name for better error messaging
      let conflictingUserName = "";

      if (invitation.invitationBy._id.toString() === userId.toString()) {
        // Current user is the inviter, so get the first invitee's name
        if (invitation.users && invitation.users.length > 0) {
          const inviteeUser = await User.findById(
            invitation.users[0].userId
          ).select("userName");
          conflictingUserName = inviteeUser
            ? inviteeUser.userName
            : "another user";
        }
      } else {
        // Current user is an invitee, so get the inviter's name
        conflictingUserName =
          invitation.invitationBy.userName || "another user";
      }

      return {
        hasConflict: true,
        conflictingInvitationId: invitation._id,
        conflictingUserName,
        conflictingDateTime: invitation.date,
        isWithinBlockedPeriod: true,
      };
    }
  }

  return { hasConflict: false };
}

async function getRescheduleCount(invitation) {
  // Count how many times this invitation has been rescheduled
  if (!invitation.isRescheduled) return 0;

  // Check if we have a stored count
  if (invitation.rescheduleCount) return invitation.rescheduleCount;

  // For backwards compatibility with existing invitations
  return invitation.rescheduleBy ? 1 : 0;
}
function extractTimeParts(timeString) {
  // First try the TimeOfDay(HH:MM) format
  const timeRegex = /TimeOfDay\((\d+):(\d+)\)/; // Format like 'TimeOfDay(22:00)'
  const match = timeString.match(timeRegex);

  if (match && match.length === 3) {
    return [parseInt(match[1]), parseInt(match[2])];
  }

  // If that fails, try the simple HH:MM format
  const simpleTimeRegex = /^(\d+):(\d+)$/; // Format like '13:55'
  const simpleMatch = timeString.match(simpleTimeRegex);

  if (simpleMatch && simpleMatch.length === 3) {
    return [parseInt(simpleMatch[1]), parseInt(simpleMatch[2])];
  }

  // If both formats fail, return null
  return null;
}
